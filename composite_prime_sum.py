#!/usr/bin/env python3
"""
合数素数和验证程序
验证合数是否能写成两个素数之和
"""

def sieve_of_eratosthenes(limit):
    """
    使用埃拉托斯特尼筛法生成素数
    """
    if limit < 2:
        return []
    
    is_prime = [True] * (limit + 1)
    is_prime[0] = is_prime[1] = False
    
    for i in range(2, int(limit**0.5) + 1):
        if is_prime[i]:
            for j in range(i*i, limit + 1, i):
                is_prime[j] = False
    
    return [i for i in range(2, limit + 1) if is_prime[i]]


def is_prime(n):
    """
    判断一个数是否为素数
    """
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0:
            return False
    return True


def is_composite(n):
    """
    判断一个数是否为合数（大于1且不是素数）
    """
    return n > 1 and not is_prime(n)


def find_prime_pairs(num, primes, prime_set):
    """
    找到一个数的所有素数对分解
    """
    pairs = []
    for p in primes:
        if p > num // 2:  # 只需要检查到num的一半，避免重复
            break
        complement = num - p
        if complement in prime_set and complement >= p:  # 确保不重复且有序
            pairs.append((p, complement))
    return pairs


def analyze_composite_numbers(start, end):
    """
    分析指定范围内的合数能否写成两个素数之和
    """
    if start < 4:
        start = 4
    
    # 生成所需的素数
    primes = sieve_of_eratosthenes(end)
    prime_set = set(primes)
    
    print(f"分析从 {start} 到 {end} 的合数能否写成两个素数之和:")
    print("=" * 70)
    
    # 找出所有合数
    composites = [n for n in range(start, end + 1) if is_composite(n)]
    
    if not composites:
        print("指定范围内没有合数")
        return
    
    total_composites = len(composites)
    can_decompose = 0
    cannot_decompose = []
    min_pairs_count = float('inf')
    min_pairs_numbers = []
    
    print("合数 | 分解数 | 示例分解")
    print("-" * 70)
    
    for num in composites:
        pairs = find_prime_pairs(num, primes, prime_set)
        pairs_count = len(pairs)
        
        if pairs:
            can_decompose += 1
            # 显示第一个分解作为示例
            example = f"{pairs[0][0]}+{pairs[0][1]}"
            print(f"{num:4d} | {pairs_count:4d}   | {example}")
            
            # 记录最少分解个数
            if pairs_count < min_pairs_count:
                min_pairs_count = pairs_count
                min_pairs_numbers = [num]
            elif pairs_count == min_pairs_count:
                min_pairs_numbers.append(num)
        else:
            cannot_decompose.append(num)
            print(f"{num:4d} | {pairs_count:4d}   | 无法分解")
    
    # 输出统计信息
    print("=" * 70)
    print(f"统计结果:")
    print(f"总合数个数: {total_composites}")
    print(f"可分解个数: {can_decompose}")
    print(f"无法分解个数: {len(cannot_decompose)}")
    if total_composites > 0:
        success_rate = can_decompose / total_composites * 100
        print(f"成功率: {success_rate:.1f}%")
    
    # 显示无法分解的合数
    if cannot_decompose:
        print(f"\n无法分解为两个素数之和的合数:")
        print(f"{cannot_decompose}")
        
        # 分析这些数的特点
        print(f"\n这些数的特点分析:")
        for num in cannot_decompose[:10]:  # 只分析前10个
            print(f"  {num}: ", end="")
            if num % 2 == 1:
                print("奇数合数")
            else:
                print("偶数合数")
    
    # 显示分解个数最少的合数
    if min_pairs_numbers and can_decompose > 0:
        print(f"\n分解个数最少的合数:")
        print(f"最少分解数: {min_pairs_count}种")
        print(f"对应的合数: {', '.join(map(str, min_pairs_numbers[:10]))}")
        
        # 显示这些数的具体分解
        if len(min_pairs_numbers) <= 5:
            print(f"具体分解:")
            for num in min_pairs_numbers:
                pairs = find_prime_pairs(num, primes, prime_set)
                pair_strs = [f"{p1}+{p2}" for p1, p2 in pairs]
                print(f"  {num}: {', '.join(pair_strs)}")


def analyze_by_type(start, end):
    """
    按奇偶性分析合数
    """
    if start < 4:
        start = 4
    
    primes = sieve_of_eratosthenes(end)
    prime_set = set(primes)
    
    # 分别统计奇数合数和偶数合数
    odd_composites = []
    even_composites = []
    
    for n in range(start, end + 1):
        if is_composite(n):
            if n % 2 == 0:
                even_composites.append(n)
            else:
                odd_composites.append(n)
    
    print(f"\n按奇偶性分析合数 (从 {start} 到 {end}):")
    print("=" * 70)
    
    # 分析偶数合数
    if even_composites:
        print(f"偶数合数分析 (共{len(even_composites)}个):")
        even_can_decompose = 0
        for num in even_composites[:10]:  # 只显示前10个
            pairs = find_prime_pairs(num, primes, prime_set)
            if pairs:
                even_can_decompose += 1
                print(f"  {num}: {len(pairs)}种分解 - {pairs[0][0]}+{pairs[0][1]}")
            else:
                print(f"  {num}: 无法分解")
        
        if len(even_composites) > 10:
            # 统计所有偶数合数
            for num in even_composites[10:]:
                pairs = find_prime_pairs(num, primes, prime_set)
                if pairs:
                    even_can_decompose += 1
        
        print(f"  偶数合数成功率: {even_can_decompose/len(even_composites)*100:.1f}%")
    
    # 分析奇数合数
    if odd_composites:
        print(f"\n奇数合数分析 (共{len(odd_composites)}个):")
        odd_can_decompose = 0
        for num in odd_composites[:10]:  # 只显示前10个
            pairs = find_prime_pairs(num, primes, prime_set)
            if pairs:
                odd_can_decompose += 1
                print(f"  {num}: {len(pairs)}种分解 - {pairs[0][0]}+{pairs[0][1]}")
            else:
                print(f"  {num}: 无法分解")
        
        if len(odd_composites) > 10:
            # 统计所有奇数合数
            for num in odd_composites[10:]:
                pairs = find_prime_pairs(num, primes, prime_set)
                if pairs:
                    odd_can_decompose += 1
        
        print(f"  奇数合数成功率: {odd_can_decompose/len(odd_composites)*100:.1f}%")


def main():
    """
    主函数
    """
    print("合数素数和验证程序")
    print("=" * 50)
    print("验证合数是否能写成两个素数之和")
    print()
    
    try:
        # 获取用户输入
        start_input = input("请输入起始数字 (默认4): ").strip()
        if not start_input:
            start = 4
        else:
            start = int(start_input)
        
        end_input = input("请输入结束数字 (默认50): ").strip()
        if not end_input:
            end = 50
        else:
            end = int(end_input)
        
        # 验证输入
        if start > end:
            print("起始数字不能大于结束数字")
            return
        
        if end < 4:
            print("结束数字至少应该为4")
            return
        
        print()
        # 执行分析
        analyze_composite_numbers(start, end)
        
        # 按奇偶性分析
        analyze_by_type(start, end)
        
        # 询问是否继续
        print()
        continue_choice = input("是否继续分析其他范围？(y/n): ").strip().lower()
        if continue_choice == 'y':
            print()
            main()
    
    except ValueError:
        print("错误：请输入有效的数字！")
    except KeyboardInterrupt:
        print("\n程序被用户中断。")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
