#!/usr/bin/env python3
"""
哥德巴赫猜想分解计数程序
显示每个偶数有多少种素数对分解
"""

def sieve_of_eratosthenes(limit):
    """
    使用埃拉托斯特尼筛法生成素数
    """
    if limit < 2:
        return []
    
    is_prime = [True] * (limit + 1)
    is_prime[0] = is_prime[1] = False
    
    for i in range(2, int(limit**0.5) + 1):
        if is_prime[i]:
            for j in range(i*i, limit + 1, i):
                is_prime[j] = False
    
    return [i for i in range(2, limit + 1) if is_prime[i]]


def count_goldbach_pairs(num, primes, prime_set):
    """
    计算一个偶数的素数对分解数量
    """
    count = 0
    for p in primes:
        if p > num // 2:
            break
        if (num - p) in prime_set:
            count += 1
    return count


def show_goldbach_counts(start, end):
    """
    显示从start到end的每个偶数的分解数量
    """
    if start < 4:
        start = 4
    if start % 2 != 0:
        start += 1
    
    primes = sieve_of_eratosthenes(end)
    prime_set = set(primes)
    
    print(f"偶数的哥德巴赫分解数量统计 (从{start}到{end}):")
    print("=" * 50)
    print("偶数  | 分解数量 | 示例分解")
    print("-" * 50)
    
    total_pairs = 0
    verified_count = 0
    
    for num in range(start, end + 1, 2):
        pairs = []
        for p in primes:
            if p > num // 2:
                break
            complement = num - p
            if complement in prime_set:
                pairs.append((p, complement))
        
        count = len(pairs)
        total_pairs += count
        
        if count > 0:
            verified_count += 1
            # 显示第一个分解作为示例
            example = f"{pairs[0][0]}+{pairs[0][1]}"
            print(f"{num:4d}  |   {count:4d}   | {example}")
        else:
            print(f"{num:4d}  |   {count:4d}   | 无分解")
    
    # 统计信息
    total_evens = (end - start) // 2 + 1
    print("-" * 50)
    print(f"统计摘要:")
    print(f"总偶数个数: {total_evens}")
    print(f"可分解个数: {verified_count}")
    print(f"总分解数量: {total_pairs}")
    if verified_count > 0:
        print(f"平均每个偶数的分解数: {total_pairs/verified_count:.1f}")
    print(f"成功率: {verified_count/total_evens*100:.1f}%")


def show_detailed_analysis(n):
    """
    显示详细的分解分析
    """
    primes = sieve_of_eratosthenes(n)
    prime_set = set(primes)
    
    print(f"详细分解分析 (4到{n}):")
    print("=" * 60)
    
    # 统计分解数量的分布
    distribution = {}
    max_pairs = 0
    max_number = 0
    
    for num in range(4, n + 1, 2):
        count = count_goldbach_pairs(num, primes, prime_set)
        
        if count > 0:
            if count not in distribution:
                distribution[count] = []
            distribution[count].append(num)
            
            if count > max_pairs:
                max_pairs = count
                max_number = num
    
    # 显示分布统计
    print("分解数量分布:")
    for count in sorted(distribution.keys()):
        numbers = distribution[count]
        print(f"{count:2d}种分解: {len(numbers):3d}个偶数 - 例如: {numbers[:5]}")
        if len(numbers) > 5:
            print(f"          (还有{len(numbers)-5}个...)")
    
    print(f"\n分解数最多的偶数: {max_number} (有{max_pairs}种分解)")
    
    # 显示这个数的所有分解
    if max_number > 0:
        print(f"\n{max_number}的所有分解:")
        pairs = []
        for p in primes:
            if p > max_number // 2:
                break
            complement = max_number - p
            if complement in prime_set:
                pairs.append((p, complement))
        
        for i, (p1, p2) in enumerate(pairs, 1):
            print(f"  {i:2d}. {p1} + {p2}")


def main():
    """
    主函数
    """
    print("哥德巴赫猜想分解计数程序")
    print("=" * 50)
    
    try:
        n = input("请输入要分析到的数字 (默认50): ").strip()
        if not n:
            n = 50
        else:
            n = int(n)
        
        print("\n选择显示模式:")
        print("1. 简单列表 (显示每个偶数的分解数量)")
        print("2. 详细分析 (显示分解数量的分布统计)")
        
        mode = input("请选择模式 (1或2，默认1): ").strip()
        
        if mode == "2":
            show_detailed_analysis(n)
        else:
            show_goldbach_counts(4, n)
        
        # 询问是否查看特定数字
        while True:
            target = input(f"\n输入一个偶数(4-{n})查看其所有分解，或按回车退出: ").strip()
            if not target:
                break
            
            try:
                target = int(target)
                if target < 4 or target > n or target % 2 != 0:
                    print("请输入一个在范围内的偶数")
                    continue
                
                primes = sieve_of_eratosthenes(n)
                prime_set = set(primes)
                pairs = []
                
                for p in primes:
                    if p > target // 2:
                        break
                    complement = target - p
                    if complement in prime_set:
                        pairs.append((p, complement))
                
                if pairs:
                    print(f"\n{target}的所有{len(pairs)}种分解:")
                    for i, (p1, p2) in enumerate(pairs, 1):
                        print(f"  {i:2d}. {p1} + {p2}")
                else:
                    print(f"{target}无法分解为两个素数的和")
                    
            except ValueError:
                print("请输入有效的数字")
    
    except ValueError:
        print("错误：请输入有效的数字！")
    except KeyboardInterrupt:
        print("\n程序被用户中断。")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
