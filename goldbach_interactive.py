#!/usr/bin/env python3
"""
哥德巴赫猜想交互式验证程序
用户输入范围，输出每个偶数的分解数量和示例
"""

def sieve_of_eratosthenes(limit):
    """
    使用埃拉托斯特尼筛法生成素数
    """
    if limit < 2:
        return []
    
    is_prime = [True] * (limit + 1)
    is_prime[0] = is_prime[1] = False
    
    for i in range(2, int(limit**0.5) + 1):
        if is_prime[i]:
            for j in range(i*i, limit + 1, i):
                is_prime[j] = False
    
    return [i for i in range(2, limit + 1) if is_prime[i]]


def find_goldbach_pairs(num, primes, prime_set):
    """
    找到一个偶数的所有素数对分解
    """
    pairs = []
    for p in primes:
        if p > num // 2:  # 只需要检查到num的一半，避免重复
            break
        complement = num - p
        if complement in prime_set:
            pairs.append((p, complement))
    return pairs


def verify_goldbach_range(start, end):
    """
    验证指定范围内的偶数的哥德巴赫分解
    """
    # 确保start是偶数且至少为4
    if start < 4:
        start = 4
    if start % 2 != 0:
        start += 1

    # 生成所需的素数
    primes = sieve_of_eratosthenes(end)
    prime_set = set(primes)

    print(f"验证从 {start} 到 {end} 的偶数的哥德巴赫分解:")
    print("=" * 60)

    total_evens = 0
    verified_count = 0
    min_pairs_count = float('inf')
    min_pairs_numbers = []

    for num in range(start, end + 1, 2):
        total_evens += 1
        pairs = find_goldbach_pairs(num, primes, prime_set)
        pairs_count = len(pairs)

        if pairs:
            verified_count += 1
            # 只显示分解个数
            print(f"{num:3d}: {pairs_count:2d}种分解")

            # 记录最少分解个数
            if pairs_count < min_pairs_count:
                min_pairs_count = pairs_count
                min_pairs_numbers = [num]
            elif pairs_count == min_pairs_count:
                min_pairs_numbers.append(num)
        else:
            print(f"{num:3d}:  0种分解")

    # 输出统计信息
    print("=" * 60)
    print(f"统计结果:")
    print(f"总偶数个数: {total_evens}")
    print(f"成功分解个数: {verified_count}")
    print(f"失败个数: {total_evens - verified_count}")
    if total_evens > 0:
        success_rate = verified_count / total_evens * 100
        print(f"成功率: {success_rate:.1f}%")

    # 显示分解个数最少的偶数
    if min_pairs_numbers:
        print(f"\n分解个数最少的偶数:")
        print(f"最少分解数: {min_pairs_count}种")
        print(f"对应的偶数: {', '.join(map(str, min_pairs_numbers))}")

        # 显示这些数的具体分解
        if len(min_pairs_numbers) <= 5:  # 只显示前5个的详细分解
            for num in min_pairs_numbers[:5]:
                pairs = find_goldbach_pairs(num, primes, prime_set)
                pair_strs = [f"{p1}+{p2}" for p1, p2 in pairs]
                print(f"  {num}: {', '.join(pair_strs)}")
        else:
            # 只显示第一个的详细分解
            num = min_pairs_numbers[0]
            pairs = find_goldbach_pairs(num, primes, prime_set)
            pair_strs = [f"{p1}+{p2}" for p1, p2 in pairs]
            print(f"  {num}: {', '.join(pair_strs)} (示例)")
    else:
        print(f"\n范围内没有可分解的偶数")


def main():
    """
    主函数
    """
    print("哥德巴赫猜想交互式验证程序")
    print("=" * 50)
    print("输入验证范围，程序将显示每个偶数的所有素数对分解")
    print()
    
    while True:
        try:
            # 获取用户输入
            start_input = input("请输入起始数字 (默认4): ").strip()
            if not start_input:
                start = 4
            else:
                start = int(start_input)
            
            end_input = input("请输入结束数字 (默认30): ").strip()
            if not end_input:
                end = 30
            else:
                end = int(end_input)
            
            # 验证输入
            if start > end:
                print("起始数字不能大于结束数字，请重新输入。")
                continue
            
            if end < 4:
                print("结束数字至少应该为4，请重新输入。")
                continue
            
            print()
            # 执行验证
            verify_goldbach_range(start, end)
            
            # 询问是否继续
            print()
            continue_choice = input("是否继续验证其他范围？(y/n): ").strip().lower()
            if continue_choice != 'y':
                break
            print()
        
        except ValueError:
            print("错误：请输入有效的数字！")
            print()
        except KeyboardInterrupt:
            print("\n程序被用户中断。")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            print()
    
    print("感谢使用哥德巴赫猜想验证程序！")


if __name__ == "__main__":
    main()
