#!/usr/bin/env python3
"""
哥德巴赫猜想简化验证程序
快速统计从4到n的偶数中，能表示为两个素数和的个数
"""

def sieve_of_eratosthenes(limit):
    """
    使用埃拉托斯特尼筛法生成素数，效率更高
    
    Args:
        limit (int): 上限
        
    Returns:
        list: 素数列表
    """
    if limit < 2:
        return []
    
    # 创建布尔数组
    is_prime = [True] * (limit + 1)
    is_prime[0] = is_prime[1] = False
    
    for i in range(2, int(limit**0.5) + 1):
        if is_prime[i]:
            # 标记i的倍数为非素数
            for j in range(i*i, limit + 1, i):
                is_prime[j] = False
    
    # 收集所有素数
    return [i for i in range(2, limit + 1) if is_prime[i]]


def count_goldbach_pairs(num, primes, prime_set):
    """
    计算一个偶数有多少种素数对分解

    Args:
        num (int): 要分解的偶数
        primes (list): 素数列表
        prime_set (set): 素数集合（用于快速查找）

    Returns:
        list: 所有素数对 [(p1, p2), ...]
    """
    pairs = []
    for p in primes:
        if p > num // 2:  # 只需要检查到num的一半，避免重复
            break
        complement = num - p
        if complement in prime_set:
            pairs.append((p, complement))
    return pairs


def analyze_goldbach_conjecture(n, show_details=True):
    """
    分析从4到n的偶数的哥德巴赫分解情况

    Args:
        n (int): 上限
        show_details (bool): 是否显示每个数的详细信息

    Returns:
        dict: 分析结果
    """
    if n < 4:
        return {'total_even_count': 0, 'verified_count': 0, 'failed_numbers': [], 'details': []}

    # 生成所需的素数
    primes = sieve_of_eratosthenes(n)
    prime_set = set(primes)

    verified_count = 0
    total_even_count = 0
    failed_numbers = []
    details = []

    print(f"分析从4到{n}的偶数的哥德巴赫分解:")
    print("=" * 60)

    # 检查所有偶数
    for num in range(4, n + 1, 2):
        total_even_count += 1
        pairs = count_goldbach_pairs(num, primes, prime_set)

        if pairs:
            verified_count += 1
            detail = {
                'number': num,
                'pairs_count': len(pairs),
                'pairs': pairs
            }
            details.append(detail)

            if show_details:
                print(f"{num:4d}: 有 {len(pairs):2d} 种分解 - ", end="")
                # 显示前几个分解
                display_pairs = pairs[:3]  # 只显示前3个
                pair_strs = [f"{p1}+{p2}" for p1, p2 in display_pairs]
                print(", ".join(pair_strs), end="")
                if len(pairs) > 3:
                    print(f", ... (共{len(pairs)}种)")
                else:
                    print()
        else:
            failed_numbers.append(num)
            if show_details:
                print(f"{num:4d}: 无法分解为两个素数的和！")

    return {
        'total_even_count': total_even_count,
        'verified_count': verified_count,
        'failed_numbers': failed_numbers,
        'details': details
    }


def main():
    """
    主函数 - 显示每个偶数的素数对分解数量
    """
    print("哥德巴赫猜想详细分析程序")
    print("=" * 50)

    try:
        n = input("请输入要分析到的数字 (默认100): ").strip()
        if not n:
            n = 100
        else:
            n = int(n)

        # 询问是否显示详细信息
        show_details = True
        if n > 100:
            choice = input(f"数字较大({n})，是否显示每个数的详细分解？(y/n，默认n): ").strip().lower()
            show_details = choice == 'y'

        print(f"\n正在分析从4到{n}的所有偶数...")

        # 执行分析
        results = analyze_goldbach_conjecture(n, show_details)

        # 输出摘要结果
        print(f"\n" + "=" * 60)
        print("分析结果摘要:")
        print(f"总偶数个数: {results['total_even_count']}")
        print(f"验证成功个数: {results['verified_count']}")
        print(f"验证失败个数: {len(results['failed_numbers'])}")

        if results['total_even_count'] > 0:
            success_rate = results['verified_count'] / results['total_even_count'] * 100
            print(f"成功率: {success_rate:.2f}%")

        if results['failed_numbers']:
            print(f"失败的数字: {results['failed_numbers']}")
        else:
            print("✅ 所有偶数都成功验证了哥德巴赫猜想！")

        # 统计分解数量的分布
        if results['details']:
            pair_counts = [detail['pairs_count'] for detail in results['details']]
            min_pairs = min(pair_counts)
            max_pairs = max(pair_counts)
            avg_pairs = sum(pair_counts) / len(pair_counts)

            print(f"\n素数对分解统计:")
            print(f"- 最少分解数: {min_pairs}")
            print(f"- 最多分解数: {max_pairs}")
            print(f"- 平均分解数: {avg_pairs:.1f}")

            # 找出分解数最多的几个数
            max_detail = max(results['details'], key=lambda x: x['pairs_count'])
            print(f"- 分解数最多的偶数: {max_detail['number']} (有{max_detail['pairs_count']}种分解)")

        # 询问是否显示特定数字的所有分解
        if not show_details and results['details']:
            show_specific = input("\n是否查看特定数字的所有分解？(输入数字，或按回车跳过): ").strip()
            if show_specific.isdigit():
                target = int(show_specific)
                if target % 2 == 0 and 4 <= target <= n:
                    target_detail = next((d for d in results['details'] if d['number'] == target), None)
                    if target_detail:
                        print(f"\n{target} 的所有 {target_detail['pairs_count']} 种分解:")
                        for i, (p1, p2) in enumerate(target_detail['pairs'], 1):
                            print(f"  {i:2d}. {p1} + {p2}")
                    else:
                        print(f"{target} 无法分解为两个素数的和")
                else:
                    print("请输入一个在范围内的偶数")

    except ValueError:
        print("错误：请输入有效的数字！")
    except KeyboardInterrupt:
        print("\n程序被用户中断。")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
