#!/usr/bin/env python3
"""
哥德巴赫猜想简化验证程序
快速统计从4到n的偶数中，能表示为两个素数和的个数
"""

def sieve_of_eratosthenes(limit):
    """
    使用埃拉托斯特尼筛法生成素数，效率更高
    
    Args:
        limit (int): 上限
        
    Returns:
        list: 素数列表
    """
    if limit < 2:
        return []
    
    # 创建布尔数组
    is_prime = [True] * (limit + 1)
    is_prime[0] = is_prime[1] = False
    
    for i in range(2, int(limit**0.5) + 1):
        if is_prime[i]:
            # 标记i的倍数为非素数
            for j in range(i*i, limit + 1, i):
                is_prime[j] = False
    
    # 收集所有素数
    return [i for i in range(2, limit + 1) if is_prime[i]]


def count_goldbach_verifications(n):
    """
    统计从4到n的偶数中，能表示为两个素数和的个数
    
    Args:
        n (int): 上限
        
    Returns:
        tuple: (验证成功的个数, 总偶数个数, 失败的数字列表)
    """
    if n < 4:
        return 0, 0, []
    
    # 生成所需的素数
    primes = sieve_of_eratosthenes(n)
    prime_set = set(primes)
    
    verified_count = 0
    total_even_count = 0
    failed_numbers = []
    
    # 检查所有偶数
    for num in range(4, n + 1, 2):
        total_even_count += 1
        found_pair = False
        
        # 查找是否存在素数对
        for p in primes:
            if p > num // 2:  # 只需要检查到num的一半
                break
            if (num - p) in prime_set:
                found_pair = True
                break
        
        if found_pair:
            verified_count += 1
        else:
            failed_numbers.append(num)
    
    return verified_count, total_even_count, failed_numbers


def main():
    """
    主函数 - 快速验证版本
    """
    print("哥德巴赫猜想快速验证程序")
    print("=" * 50)
    
    try:
        n = input("请输入要验证到的数字 (默认1000): ").strip()
        if not n:
            n = 1000
        else:
            n = int(n)
        
        print(f"\n正在验证从4到{n}的所有偶数...")
        
        # 执行验证
        verified, total, failed = count_goldbach_verifications(n)
        
        # 输出结果
        print(f"\n验证结果:")
        print(f"总偶数个数: {total}")
        print(f"验证成功个数: {verified}")
        print(f"验证失败个数: {len(failed)}")
        
        if total > 0:
            success_rate = verified / total * 100
            print(f"成功率: {success_rate:.2f}%")
        
        if failed:
            print(f"失败的数字: {failed}")
        else:
            print("✅ 所有偶数都成功验证了哥德巴赫猜想！")
        
        # 显示一些统计信息
        if verified > 0:
            print(f"\n统计信息:")
            print(f"- 在{n}以内，共有{total}个偶数")
            print(f"- 其中{verified}个可以表示为两个素数的和")
            print(f"- 这支持了哥德巴赫猜想在{n}以内的正确性")
    
    except ValueError:
        print("错误：请输入有效的数字！")
    except KeyboardInterrupt:
        print("\n程序被用户中断。")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
