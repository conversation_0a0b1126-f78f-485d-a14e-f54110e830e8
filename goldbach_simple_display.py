#!/usr/bin/env python3
"""
哥德巴赫猜想简单显示程序
快速显示指定范围内每个偶数的分解数量和示例
"""

def sieve_of_eratosthenes(limit):
    """生成素数"""
    if limit < 2:
        return []
    
    is_prime = [True] * (limit + 1)
    is_prime[0] = is_prime[1] = False
    
    for i in range(2, int(limit**0.5) + 1):
        if is_prime[i]:
            for j in range(i*i, limit + 1, i):
                is_prime[j] = False
    
    return [i for i in range(2, limit + 1) if is_prime[i]]


def goldbach_display(start, end):
    """显示哥德巴赫分解"""
    if start < 4:
        start = 4
    if start % 2 != 0:
        start += 1
    
    primes = sieve_of_eratosthenes(end)
    prime_set = set(primes)
    
    for num in range(start, end + 1, 2):
        pairs = []
        for p in primes:
            if p > num // 2:
                break
            if (num - p) in prime_set:
                pairs.append((p, num - p))
        
        if pairs:
            pair_strs = [f"{p1}+{p2}" for p1, p2 in pairs]
            print(f"{num:3d}: {len(pairs):2d}种分解 - {', '.join(pair_strs)}")
        else:
            print(f"{num:3d}:  0种分解 - 无法分解")


def main():
    """主函数"""
    print("哥德巴赫猜想分解显示")
    print("=" * 40)
    
    try:
        start = int(input("起始数字 (默认4): ") or "4")
        end = int(input("结束数字 (默认30): ") or "30")
        
        print(f"\n从 {start} 到 {end} 的偶数分解:")
        print("-" * 40)
        goldbach_display(start, end)
        
    except ValueError:
        print("请输入有效数字！")
    except KeyboardInterrupt:
        print("\n程序中断")


if __name__ == "__main__":
    main()
