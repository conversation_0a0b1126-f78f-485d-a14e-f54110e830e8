#!/usr/bin/env python3
"""
哥德巴赫猜想验证程序

哥德巴赫猜想：任何大于2的偶数都可以表示为两个素数的和。
本程序验证从4开始到n的所有偶数，计算出能表示为两个素数和的偶数个数。
"""

def is_prime(n):
    """
    判断一个数是否为素数
    
    Args:
        n (int): 要判断的数
        
    Returns:
        bool: 如果是素数返回True，否则返回False
    """
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    
    # 只需要检查到sqrt(n)
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0:
            return False
    return True


def generate_primes(limit):
    """
    生成小于等于limit的所有素数
    
    Args:
        limit (int): 上限
        
    Returns:
        list: 素数列表
    """
    primes = []
    for i in range(2, limit + 1):
        if is_prime(i):
            primes.append(i)
    return primes


def find_goldbach_pairs(n, primes):
    """
    找到偶数n的所有哥德巴赫分解（两个素数的和）
    
    Args:
        n (int): 要分解的偶数
        primes (list): 素数列表
        
    Returns:
        list: 所有可能的素数对 [(p1, p2), ...]
    """
    pairs = []
    prime_set = set(primes)
    
    for p in primes:
        if p > n // 2:  # 避免重复，只检查到n的一半
            break
        complement = n - p
        if complement in prime_set:
            pairs.append((p, complement))
    
    return pairs


def verify_goldbach_conjecture(start=4, end=100):
    """
    验证哥德巴赫猜想
    
    Args:
        start (int): 起始偶数，默认为4
        end (int): 结束数字，默认为100
        
    Returns:
        dict: 验证结果统计
    """
    # 确保start是偶数且大于等于4
    if start < 4:
        start = 4
    if start % 2 != 0:
        start += 1
    
    # 生成所需的素数
    primes = generate_primes(end)
    
    results = {
        'total_even_numbers': 0,
        'verified_count': 0,
        'failed_numbers': [],
        'details': []
    }
    
    print(f"验证哥德巴赫猜想：从 {start} 到 {end} 的所有偶数")
    print("=" * 60)
    
    for n in range(start, end + 1, 2):  # 只检查偶数
        results['total_even_numbers'] += 1
        pairs = find_goldbach_pairs(n, primes)
        
        if pairs:
            results['verified_count'] += 1
            # 只显示第一个分解
            first_pair = pairs[0]
            detail = {
                'number': n,
                'pairs_count': len(pairs),
                'first_pair': first_pair,
                'all_pairs': pairs
            }
            results['details'].append(detail)
            
            print(f"{n:3d} = {first_pair[0]:2d} + {first_pair[1]:2d} "
                  f"(共找到 {len(pairs)} 种分解)")
        else:
            results['failed_numbers'].append(n)
            print(f"{n:3d} = 无法分解为两个素数的和！")
    
    return results


def print_summary(results):
    """
    打印验证结果摘要
    
    Args:
        results (dict): 验证结果
    """
    print("\n" + "=" * 60)
    print("验证结果摘要：")
    print(f"总偶数个数: {results['total_even_numbers']}")
    print(f"验证成功个数: {results['verified_count']}")
    print(f"验证失败个数: {len(results['failed_numbers'])}")
    print(f"成功率: {results['verified_count']/results['total_even_numbers']*100:.1f}%")
    
    if results['failed_numbers']:
        print(f"失败的数字: {results['failed_numbers']}")
    else:
        print("✅ 所有偶数都成功验证了哥德巴赫猜想！")


def main():
    """
    主函数
    """
    print("哥德巴赫猜想验证程序")
    print("=" * 60)
    
    try:
        # 获取用户输入
        end_num = input("请输入要验证到的数字 (默认100): ").strip()
        if not end_num:
            end_num = 100
        else:
            end_num = int(end_num)
        
        start_num = input("请输入起始数字 (默认4): ").strip()
        if not start_num:
            start_num = 4
        else:
            start_num = int(start_num)
        
        # 验证哥德巴赫猜想
        results = verify_goldbach_conjecture(start_num, end_num)
        
        # 打印摘要
        print_summary(results)
        
        # 询问是否显示详细信息
        show_details = input("\n是否显示所有分解详情？(y/n): ").strip().lower()
        if show_details == 'y':
            print("\n详细分解信息：")
            print("-" * 60)
            for detail in results['details']:
                n = detail['number']
                pairs = detail['all_pairs']
                print(f"{n} 的所有分解:")
                for i, (p1, p2) in enumerate(pairs, 1):
                    print(f"  {i}. {p1} + {p2}")
                print()
    
    except ValueError:
        print("错误：请输入有效的数字！")
    except KeyboardInterrupt:
        print("\n程序被用户中断。")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
