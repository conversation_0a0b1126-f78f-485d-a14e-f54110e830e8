#!/usr/bin/env python3
"""
测试哥德巴赫猜想程序的功能
"""

# 导入我们的模块
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from goldbach_count import sieve_of_eratosthenes, count_goldbach_pairs, show_goldbach_counts

def test_small_range():
    """
    测试小范围的哥德巴赫分解
    """
    print("测试从4到30的偶数的哥德巴赫分解:")
    print("=" * 60)
    
    n = 30
    primes = sieve_of_eratosthenes(n)
    prime_set = set(primes)
    
    print(f"生成的素数: {primes}")
    print()
    
    for num in range(4, n + 1, 2):
        pairs = []
        for p in primes:
            if p > num // 2:
                break
            complement = num - p
            if complement in prime_set:
                pairs.append((p, complement))
        
        print(f"{num:2d}: {len(pairs):2d}种分解 - ", end="")
        if pairs:
            pair_strs = [f"{p1}+{p2}" for p1, p2 in pairs]
            print(", ".join(pair_strs))
        else:
            print("无分解")


def test_specific_numbers():
    """
    测试特定数字的分解
    """
    print("\n测试特定数字的详细分解:")
    print("=" * 60)
    
    test_numbers = [10, 20, 30, 50, 100]
    
    for num in test_numbers:
        primes = sieve_of_eratosthenes(num)
        prime_set = set(primes)
        pairs = []
        
        for p in primes:
            if p > num // 2:
                break
            complement = num - p
            if complement in prime_set:
                pairs.append((p, complement))
        
        print(f"\n{num}的分解 (共{len(pairs)}种):")
        for i, (p1, p2) in enumerate(pairs, 1):
            print(f"  {i:2d}. {p1} + {p2}")


def analyze_pattern():
    """
    分析分解数量的模式
    """
    print("\n分析分解数量的模式:")
    print("=" * 60)
    
    n = 100
    primes = sieve_of_eratosthenes(n)
    prime_set = set(primes)
    
    # 统计每个偶数的分解数量
    counts = []
    for num in range(4, n + 1, 2):
        count = count_goldbach_pairs(num, primes, prime_set)
        counts.append((num, count))
    
    # 找出分解数最多的几个数
    counts.sort(key=lambda x: x[1], reverse=True)
    
    print("分解数最多的10个偶数:")
    for i, (num, count) in enumerate(counts[:10], 1):
        print(f"{i:2d}. {num:3d}: {count:2d}种分解")
    
    # 统计分解数量的分布
    distribution = {}
    for num, count in counts:
        if count not in distribution:
            distribution[count] = 0
        distribution[count] += 1
    
    print(f"\n分解数量分布 (在4到{n}范围内):")
    for count in sorted(distribution.keys()):
        print(f"{count:2d}种分解: {distribution[count]:2d}个偶数")


def main():
    """
    运行所有测试
    """
    print("哥德巴赫猜想程序测试")
    print("=" * 60)
    
    # 测试小范围
    test_small_range()
    
    # 测试特定数字
    test_specific_numbers()
    
    # 分析模式
    analyze_pattern()
    
    print(f"\n测试完成！")
    print("你可以运行以下程序进行交互式体验:")
    print("- python goldbach_count.py (显示每个偶数的分解数量)")
    print("- python goldbach_simple.py (详细分析)")
    print("- python goldbach_verification.py (完整验证)")


if __name__ == "__main__":
    main()
