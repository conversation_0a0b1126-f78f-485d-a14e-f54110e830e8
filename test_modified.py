#!/usr/bin/env python3
"""
测试修改后的哥德巴赫猜想程序功能
"""

def sieve_of_eratosthenes(limit):
    """生成素数"""
    if limit < 2:
        return []
    
    is_prime = [True] * (limit + 1)
    is_prime[0] = is_prime[1] = False
    
    for i in range(2, int(limit**0.5) + 1):
        if is_prime[i]:
            for j in range(i*i, limit + 1, i):
                is_prime[j] = False
    
    return [i for i in range(2, limit + 1) if is_prime[i]]


def find_goldbach_pairs(num, primes, prime_set):
    """找到一个偶数的所有素数对分解"""
    pairs = []
    for p in primes:
        if p > num // 2:
            break
        complement = num - p
        if complement in prime_set:
            pairs.append((p, complement))
    return pairs


def test_modified_output():
    """测试修改后的输出格式"""
    start, end = 4, 50
    
    # 生成所需的素数
    primes = sieve_of_eratosthenes(end)
    prime_set = set(primes)
    
    print(f"验证从 {start} 到 {end} 的偶数的哥德巴赫分解:")
    print("=" * 60)
    
    total_evens = 0
    verified_count = 0
    min_pairs_count = float('inf')
    min_pairs_numbers = []
    
    for num in range(start, end + 1, 2):
        total_evens += 1
        pairs = find_goldbach_pairs(num, primes, prime_set)
        pairs_count = len(pairs)
        
        if pairs:
            verified_count += 1
            # 只显示分解个数
            print(f"{num:3d}: {pairs_count:2d}种分解")
            
            # 记录最少分解个数
            if pairs_count < min_pairs_count:
                min_pairs_count = pairs_count
                min_pairs_numbers = [num]
            elif pairs_count == min_pairs_count:
                min_pairs_numbers.append(num)
        else:
            print(f"{num:3d}:  0种分解")
    
    # 输出统计信息
    print("=" * 60)
    print(f"统计结果:")
    print(f"总偶数个数: {total_evens}")
    print(f"成功分解个数: {verified_count}")
    print(f"失败个数: {total_evens - verified_count}")
    if total_evens > 0:
        success_rate = verified_count / total_evens * 100
        print(f"成功率: {success_rate:.1f}%")
    
    # 显示分解个数最少的偶数
    if min_pairs_numbers:
        print(f"\n分解个数最少的偶数:")
        print(f"最少分解数: {min_pairs_count}种")
        print(f"对应的偶数: {', '.join(map(str, min_pairs_numbers))}")
        
        # 显示这些数的具体分解
        print(f"具体分解:")
        for num in min_pairs_numbers[:5]:  # 只显示前5个
            pairs = find_goldbach_pairs(num, primes, prime_set)
            pair_strs = [f"{p1}+{p2}" for p1, p2 in pairs]
            print(f"  {num}: {', '.join(pair_strs)}")
    else:
        print(f"\n范围内没有可分解的偶数")


if __name__ == "__main__":
    test_modified_output()
